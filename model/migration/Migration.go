package migration

import (
	"github.com/uptrace/bun"
	"time"
)

// MigrationStatus represents the status of a migration
type MigrationStatus string

const (
	MigrationStatusPending   MigrationStatus = "PENDING"
	MigrationStatusSuccess   MigrationStatus = "SUCCESS"
	MigrationStatusFailed    MigrationStatus = "FAILED"
	MigrationStatusBaseline  MigrationStatus = "BASELINE"
	MigrationStatusIgnored   MigrationStatus = "IGNORED"
	MigrationStatusDeleted   MigrationStatus = "DELETED"
	MigrationStatusAvailable MigrationStatus = "AVAILABLE"
	MigrationStatusMissing   MigrationStatus = "MISSING"
)

// Migration represents a database migration record
type Migration struct {
	bun.BaseModel `bun:"flyway_schema_history"`
	
	InstalledRank   int             `bun:",pk,autoincrement" json:"installedRank"`
	Version         string          `bun:"type:varchar(50)" json:"version"`
	Description     string          `bun:"type:varchar(200)" json:"description"`
	Type            string          `bun:"type:varchar(20)" json:"type"`
	Script          string          `bun:"type:varchar(1000)" json:"script"`
	Checksum        string          `bun:"type:varchar(100)" json:"checksum"`
	InstalledBy     string          `bun:"type:varchar(100)" json:"installedBy"`
	InstalledOn     time.Time       `bun:"type:timestamp,default:current_timestamp" json:"installedOn"`
	ExecutionTime   int             `json:"executionTime"` // in milliseconds
	Success         bool            `json:"success"`
}

// MigrationInfo represents migration information for status reporting
type MigrationInfo struct {
	Version         string          `json:"version"`
	Description     string          `json:"description"`
	Type            string          `json:"type"`
	Script          string          `json:"script"`
	State           MigrationStatus `json:"state"`
	InstalledOn     *time.Time      `json:"installedOn,omitempty"`
	ExecutionTime   *int            `json:"executionTime,omitempty"`
	Checksum        string          `json:"checksum"`
}

// MigrationFile represents a migration file on disk
type MigrationFile struct {
	Version     string
	Description string
	Type        string
	Script      string
	FilePath    string
	Checksum    string
}

// MigrationResult represents the result of a migration execution
type MigrationResult struct {
	Migration     *MigrationFile
	Success       bool
	Error         error
	ExecutionTime time.Duration
}

// MigrationCommand represents available migration commands
type MigrationCommand string

const (
	CommandMigrate  MigrationCommand = "migrate"
	CommandInfo     MigrationCommand = "info"
	CommandValidate MigrationCommand = "validate"
	CommandBaseline MigrationCommand = "baseline"
	CommandRepair   MigrationCommand = "repair"
	CommandClean    MigrationCommand = "clean"
)

// MigrationConfig holds configuration for the migration system
type MigrationConfig struct {
	MigrationsPath     string
	TableName          string
	BaselineVersion    string
	BaselineDescription string
	ValidateOnMigrate  bool
	CleanOnValidationError bool
	OutOfOrder         bool
	IgnoreMissingMigrations bool
	IgnoreIgnoredMigrations bool
	IgnorePendingMigrations bool
	IgnoreFutureMigrations  bool
	ValidateChecksums  bool
	InstalledBy        string
}

// DefaultMigrationConfig returns default migration configuration
func DefaultMigrationConfig() *MigrationConfig {
	return &MigrationConfig{
		MigrationsPath:          "db/migrations",
		TableName:               "flyway_schema_history",
		BaselineVersion:         "1",
		BaselineDescription:     "<< Flyway Baseline >>",
		ValidateOnMigrate:       true,
		CleanOnValidationError:  false,
		OutOfOrder:              false,
		IgnoreMissingMigrations: false,
		IgnoreIgnoredMigrations: false,
		IgnorePendingMigrations: false,
		IgnoreFutureMigrations:  false,
		ValidateChecksums:       true,
		InstalledBy:             "flyway-go",
	}
}
