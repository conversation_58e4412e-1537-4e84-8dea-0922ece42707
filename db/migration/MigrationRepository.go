package migration

import (
	"context"
	"github.com/uptrace/bun"
	"patch-central-repo/logger"
	migrationModel "patch-central-repo/model/migration"
	"time"
)

// MigrationRepository handles database operations for migrations
type MigrationRepository struct {
	dbConnection *bun.DB
	config       *migrationModel.MigrationConfig
}

// NewMigrationRepository creates a new migration repository
func NewMigrationRepository(config *migrationModel.MigrationConfig, dbConnection *bun.DB) *MigrationRepository {
	return &MigrationRepository{
		dbConnection: dbConnection,
		config:       config,
	}
}

// EnsureMigrationTableExists creates the migration history table if it doesn't exist
func (repo *MigrationRepository) EnsureMigrationTableExists() error {
	ctx := context.Background()
	
	// Check if table exists
	exists, err := repo.dbConnection.NewSelect().
		ColumnExpr("table_name").
		TableExpr("information_schema.tables").
		Where("table_schema = 'public' AND table_name = ?", repo.config.TableName).
		Exists(ctx)
	
	if err != nil {
		logger.ServiceLogger.Error("[MigrationRepository EnsureMigrationTableExists] Error checking table existence:", err.Error())
		return err
	}
	
	if !exists {
		// Create the migration table
		_, err = repo.dbConnection.NewCreateTable().
			Model((*migrationModel.Migration)(nil)).
			IfNotExists().
			Exec(ctx)
		
		if err != nil {
			logger.ServiceLogger.Error("[MigrationRepository EnsureMigrationTableExists] Error creating migration table:", err.Error())
			return err
		}
		
		logger.ServiceLogger.Info("[MigrationRepository] Created migration history table:", repo.config.TableName)
	}
	
	return nil
}

// GetAppliedMigrations returns all applied migrations ordered by installed rank
func (repo *MigrationRepository) GetAppliedMigrations() ([]migrationModel.Migration, error) {
	ctx := context.Background()
	var migrations []migrationModel.Migration
	
	err := repo.dbConnection.NewSelect().
		Model(&migrations).
		Order("installed_rank ASC").
		Scan(ctx)
	
	if err != nil {
		logger.ServiceLogger.Error("[MigrationRepository GetAppliedMigrations]", err.Error())
		return nil, err
	}
	
	return migrations, nil
}

// GetMigrationByVersion returns a migration by its version
func (repo *MigrationRepository) GetMigrationByVersion(version string) (*migrationModel.Migration, error) {
	ctx := context.Background()
	var migration migrationModel.Migration
	
	err := repo.dbConnection.NewSelect().
		Model(&migration).
		Where("version = ?", version).
		Scan(ctx)
	
	if err != nil {
		if err.Error() == "sql: no rows in result set" {
			return nil, nil // Migration not found
		}
		logger.ServiceLogger.Error("[MigrationRepository GetMigrationByVersion]", err.Error())
		return nil, err
	}
	
	return &migration, nil
}

// GetLatestMigration returns the latest applied migration
func (repo *MigrationRepository) GetLatestMigration() (*migrationModel.Migration, error) {
	ctx := context.Background()
	var migration migrationModel.Migration
	
	err := repo.dbConnection.NewSelect().
		Model(&migration).
		Order("installed_rank DESC").
		Limit(1).
		Scan(ctx)
	
	if err != nil {
		if err.Error() == "sql: no rows in result set" {
			return nil, nil // No migrations applied yet
		}
		logger.ServiceLogger.Error("[MigrationRepository GetLatestMigration]", err.Error())
		return nil, err
	}
	
	return &migration, nil
}

// RecordMigration records a successful migration
func (repo *MigrationRepository) RecordMigration(migrationFile *migrationModel.MigrationFile, executionTime time.Duration, success bool) error {
	ctx := context.Background()
	
	migration := &migrationModel.Migration{
		Version:       migrationFile.Version,
		Description:   migrationFile.Description,
		Type:          migrationFile.Type,
		Script:        migrationFile.Script,
		Checksum:      migrationFile.Checksum,
		InstalledBy:   repo.config.InstalledBy,
		InstalledOn:   time.Now(),
		ExecutionTime: int(executionTime.Milliseconds()),
		Success:       success,
	}
	
	_, err := repo.dbConnection.NewInsert().
		Model(migration).
		Exec(ctx)
	
	if err != nil {
		logger.ServiceLogger.Error("[MigrationRepository RecordMigration]", err.Error())
		return err
	}
	
	logger.ServiceLogger.Info("[MigrationRepository] Recorded migration:", migrationFile.Version)
	return nil
}

// RecordBaseline records a baseline migration
func (repo *MigrationRepository) RecordBaseline(version string, description string) error {
	ctx := context.Background()
	
	migration := &migrationModel.Migration{
		Version:       version,
		Description:   description,
		Type:          "BASELINE",
		Script:        "<< Flyway Baseline >>",
		Checksum:      "",
		InstalledBy:   repo.config.InstalledBy,
		InstalledOn:   time.Now(),
		ExecutionTime: 0,
		Success:       true,
	}
	
	_, err := repo.dbConnection.NewInsert().
		Model(migration).
		Exec(ctx)
	
	if err != nil {
		logger.ServiceLogger.Error("[MigrationRepository RecordBaseline]", err.Error())
		return err
	}
	
	logger.ServiceLogger.Info("[MigrationRepository] Recorded baseline:", version)
	return nil
}

// DeleteMigration removes a migration record (used for repair)
func (repo *MigrationRepository) DeleteMigration(version string) error {
	ctx := context.Background()
	
	_, err := repo.dbConnection.NewDelete().
		Model((*migrationModel.Migration)(nil)).
		Where("version = ?", version).
		Exec(ctx)
	
	if err != nil {
		logger.ServiceLogger.Error("[MigrationRepository DeleteMigration]", err.Error())
		return err
	}
	
	logger.ServiceLogger.Info("[MigrationRepository] Deleted migration record:", version)
	return nil
}

// UpdateMigrationChecksum updates the checksum of a migration (used for repair)
func (repo *MigrationRepository) UpdateMigrationChecksum(version string, checksum string) error {
	ctx := context.Background()
	
	_, err := repo.dbConnection.NewUpdate().
		Model((*migrationModel.Migration)(nil)).
		Set("checksum = ?", checksum).
		Where("version = ?", version).
		Exec(ctx)
	
	if err != nil {
		logger.ServiceLogger.Error("[MigrationRepository UpdateMigrationChecksum]", err.Error())
		return err
	}
	
	logger.ServiceLogger.Info("[MigrationRepository] Updated migration checksum:", version)
	return nil
}

// CleanMigrationHistory removes all migration records (used for clean)
func (repo *MigrationRepository) CleanMigrationHistory() error {
	ctx := context.Background()
	
	_, err := repo.dbConnection.NewDelete().
		Model((*migrationModel.Migration)(nil)).
		Where("1 = 1"). // Delete all records
		Exec(ctx)
	
	if err != nil {
		logger.ServiceLogger.Error("[MigrationRepository CleanMigrationHistory]", err.Error())
		return err
	}
	
	logger.ServiceLogger.Info("[MigrationRepository] Cleaned migration history")
	return nil
}

// GetMigrationCount returns the total number of applied migrations
func (repo *MigrationRepository) GetMigrationCount() (int, error) {
	ctx := context.Background()
	
	count, err := repo.dbConnection.NewSelect().
		Model((*migrationModel.Migration)(nil)).
		Count(ctx)
	
	if err != nil {
		logger.ServiceLogger.Error("[MigrationRepository GetMigrationCount]", err.Error())
		return 0, err
	}
	
	return count, nil
}
