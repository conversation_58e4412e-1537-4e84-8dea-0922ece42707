package migration

import (
	"context"
	"fmt"
	"strings"
	"time"

	"github.com/uptrace/bun"
	"patch-central-repo/logger"
	migrationModel "patch-central-repo/model/migration"
)

// MigrationExecutor executes migration scripts
type MigrationExecutor struct {
	dbConnection *bun.DB
	config       *migrationModel.MigrationConfig
	scanner      *MigrationScanner
}

// NewMigrationExecutor creates a new migration executor
func NewMigrationExecutor(config *migrationModel.MigrationConfig, dbConnection *bun.DB) *MigrationExecutor {
	return &MigrationExecutor{
		dbConnection: dbConnection,
		config:       config,
		scanner:      NewMigrationScanner(config),
	}
}

// ExecuteMigration executes a single migration
func (executor *MigrationExecutor) ExecuteMigration(migration *migrationModel.MigrationFile) *migrationModel.MigrationResult {
	logger.ServiceLogger.Info("[MigrationExecutor] Executing migration:", migration.Version, "-", migration.Description)
	
	startTime := time.Now()
	
	// Get migration content
	content, err := executor.scanner.GetMigrationContent(migration)
	if err != nil {
		return &migrationModel.MigrationResult{
			Migration:     migration,
			Success:       false,
			Error:         fmt.Errorf("failed to read migration content: %w", err),
			ExecutionTime: time.Since(startTime),
		}
	}
	
	// Execute migration in a transaction
	err = executor.executeInTransaction(content)
	executionTime := time.Since(startTime)
	
	if err != nil {
		logger.ServiceLogger.Error("[MigrationExecutor] Migration failed:", migration.Version, err.Error())
		return &migrationModel.MigrationResult{
			Migration:     migration,
			Success:       false,
			Error:         err,
			ExecutionTime: executionTime,
		}
	}
	
	logger.ServiceLogger.Info("[MigrationExecutor] Migration completed successfully:", migration.Version, "in", executionTime)
	return &migrationModel.MigrationResult{
		Migration:     migration,
		Success:       true,
		Error:         nil,
		ExecutionTime: executionTime,
	}
}

// executeInTransaction executes SQL content within a transaction
func (executor *MigrationExecutor) executeInTransaction(content string) error {
	ctx := context.Background()
	
	// Start transaction
	tx, err := executor.dbConnection.BeginTx(ctx, nil)
	if err != nil {
		return fmt.Errorf("failed to start transaction: %w", err)
	}
	
	defer func() {
		if err != nil {
			if rollbackErr := tx.Rollback(); rollbackErr != nil {
				logger.ServiceLogger.Error("[MigrationExecutor] Failed to rollback transaction:", rollbackErr.Error())
			}
		}
	}()
	
	// Split content into individual statements
	statements := executor.splitSQLStatements(content)
	
	// Execute each statement
	for i, statement := range statements {
		statement = strings.TrimSpace(statement)
		if statement == "" {
			continue
		}
		
		logger.ServiceLogger.Debug("[MigrationExecutor] Executing statement", i+1, "of", len(statements))
		
		_, err = tx.ExecContext(ctx, statement)
		if err != nil {
			return fmt.Errorf("failed to execute statement %d: %w\nStatement: %s", i+1, err, statement)
		}
	}
	
	// Commit transaction
	err = tx.Commit()
	if err != nil {
		return fmt.Errorf("failed to commit transaction: %w", err)
	}
	
	return nil
}

// splitSQLStatements splits SQL content into individual statements
func (executor *MigrationExecutor) splitSQLStatements(content string) []string {
	// Simple statement splitting by semicolon
	// This is a basic implementation - a more sophisticated parser might be needed
	// for complex SQL with semicolons in strings, etc.
	
	statements := strings.Split(content, ";")
	var cleanStatements []string
	
	for _, stmt := range statements {
		stmt = strings.TrimSpace(stmt)
		// Remove comments and empty lines
		lines := strings.Split(stmt, "\n")
		var cleanLines []string
		
		for _, line := range lines {
			line = strings.TrimSpace(line)
			// Skip empty lines and comments
			if line != "" && !strings.HasPrefix(line, "--") && !strings.HasPrefix(line, "/*") {
				cleanLines = append(cleanLines, line)
			}
		}
		
		if len(cleanLines) > 0 {
			cleanStmt := strings.Join(cleanLines, "\n")
			if cleanStmt != "" {
				cleanStatements = append(cleanStatements, cleanStmt)
			}
		}
	}
	
	return cleanStatements
}

// ValidateMigration validates a migration before execution
func (executor *MigrationExecutor) ValidateMigration(migration *migrationModel.MigrationFile) error {
	// Check if file exists
	content, err := executor.scanner.GetMigrationContent(migration)
	if err != nil {
		return fmt.Errorf("migration file not found or unreadable: %w", err)
	}
	
	// Basic SQL validation
	if strings.TrimSpace(content) == "" {
		return fmt.Errorf("migration file is empty: %s", migration.Script)
	}
	
	// Check for dangerous operations if needed
	if executor.config.ValidateOnMigrate {
		if err := executor.validateSQLContent(content); err != nil {
			return err
		}
	}
	
	return nil
}

// validateSQLContent performs basic validation of SQL content
func (executor *MigrationExecutor) validateSQLContent(content string) error {
	content = strings.ToUpper(strings.TrimSpace(content))
	
	// Check for potentially dangerous operations
	dangerousOperations := []string{
		"DROP DATABASE",
		"DROP SCHEMA",
		"TRUNCATE",
	}
	
	for _, operation := range dangerousOperations {
		if strings.Contains(content, operation) {
			logger.ServiceLogger.Warn("[MigrationExecutor] Potentially dangerous operation detected:", operation)
			// In a production system, you might want to require explicit confirmation
		}
	}
	
	return nil
}

// DryRun performs a dry run of a migration (validation only, no execution)
func (executor *MigrationExecutor) DryRun(migration *migrationModel.MigrationFile) error {
	logger.ServiceLogger.Info("[MigrationExecutor] Dry run for migration:", migration.Version, "-", migration.Description)
	
	// Validate the migration
	if err := executor.ValidateMigration(migration); err != nil {
		return fmt.Errorf("migration validation failed: %w", err)
	}
	
	// Get migration content
	content, err := executor.scanner.GetMigrationContent(migration)
	if err != nil {
		return fmt.Errorf("failed to read migration content: %w", err)
	}
	
	// Parse statements (without executing)
	statements := executor.splitSQLStatements(content)
	logger.ServiceLogger.Info("[MigrationExecutor] Dry run found", len(statements), "SQL statements")
	
	// Log statements for review
	for i, statement := range statements {
		if strings.TrimSpace(statement) != "" {
			logger.ServiceLogger.Debug("[MigrationExecutor] Statement", i+1, ":", statement)
		}
	}
	
	logger.ServiceLogger.Info("[MigrationExecutor] Dry run completed successfully for:", migration.Version)
	return nil
}

// ExecuteMigrations executes multiple migrations in sequence
func (executor *MigrationExecutor) ExecuteMigrations(migrations []*migrationModel.MigrationFile) ([]*migrationModel.MigrationResult, error) {
	var results []*migrationModel.MigrationResult
	
	logger.ServiceLogger.Info("[MigrationExecutor] Starting execution of", len(migrations), "migrations")
	
	for _, migration := range migrations {
		// Validate migration before execution
		if err := executor.ValidateMigration(migration); err != nil {
			result := &migrationModel.MigrationResult{
				Migration: migration,
				Success:   false,
				Error:     fmt.Errorf("validation failed: %w", err),
			}
			results = append(results, result)
			
			// Stop on first failure unless configured otherwise
			if !executor.config.IgnorePendingMigrations {
				return results, fmt.Errorf("migration validation failed for %s: %w", migration.Version, err)
			}
			continue
		}
		
		// Execute migration
		result := executor.ExecuteMigration(migration)
		results = append(results, result)
		
		// Stop on first failure unless configured otherwise
		if !result.Success && !executor.config.IgnorePendingMigrations {
			return results, fmt.Errorf("migration execution failed for %s: %w", migration.Version, result.Error)
		}
	}
	
	logger.ServiceLogger.Info("[MigrationExecutor] Completed execution of", len(migrations), "migrations")
	return results, nil
}

// TestConnection tests the database connection
func (executor *MigrationExecutor) TestConnection() error {
	ctx := context.Background()
	
	// Simple ping test
	_, err := executor.dbConnection.ExecContext(ctx, "SELECT 1")
	if err != nil {
		return fmt.Errorf("database connection test failed: %w", err)
	}
	
	logger.ServiceLogger.Info("[MigrationExecutor] Database connection test successful")
	return nil
}
