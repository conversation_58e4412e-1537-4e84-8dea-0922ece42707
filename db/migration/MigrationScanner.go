package migration

import (
	"crypto/md5"
	"fmt"
	"io/fs"
	"os"
	"path/filepath"
	"regexp"
	"sort"
	"strconv"
	"strings"
	
	"patch-central-repo/logger"
	migrationModel "patch-central-repo/model/migration"
)

// MigrationScanner scans for migration files and parses them
type MigrationScanner struct {
	config *migrationModel.MigrationConfig
}

// NewMigrationScanner creates a new migration scanner
func NewMigrationScanner(config *migrationModel.MigrationConfig) *MigrationScanner {
	return &MigrationScanner{
		config: config,
	}
}

// ScanMigrations scans the migrations directory and returns all migration files
func (scanner *MigrationScanner) ScanMigrations() ([]*migrationModel.MigrationFile, error) {
	var migrations []*migrationModel.MigrationFile
	
	// Check if migrations directory exists
	if _, err := os.Stat(scanner.config.MigrationsPath); os.IsNotExist(err) {
		logger.ServiceLogger.Warn("[MigrationScanner] Migrations directory does not exist:", scanner.config.MigrationsPath)
		return migrations, nil
	}
	
	// Walk through the migrations directory
	err := filepath.WalkDir(scanner.config.MigrationsPath, func(path string, d fs.DirEntry, err error) error {
		if err != nil {
			return err
		}
		
		// Skip directories
		if d.IsDir() {
			return nil
		}
		
		// Only process SQL files
		if !strings.HasSuffix(strings.ToLower(d.Name()), ".sql") {
			return nil
		}
		
		// Parse migration file
		migration, parseErr := scanner.parseMigrationFile(path, d.Name())
		if parseErr != nil {
			logger.ServiceLogger.Error("[MigrationScanner] Error parsing migration file:", path, parseErr.Error())
			return parseErr
		}
		
		if migration != nil {
			migrations = append(migrations, migration)
		}
		
		return nil
	})
	
	if err != nil {
		logger.ServiceLogger.Error("[MigrationScanner] Error scanning migrations directory:", err.Error())
		return nil, err
	}
	
	// Sort migrations by version
	sort.Slice(migrations, func(i, j int) bool {
		return scanner.compareVersions(migrations[i].Version, migrations[j].Version) < 0
	})
	
	logger.ServiceLogger.Info("[MigrationScanner] Found", len(migrations), "migration files")
	return migrations, nil
}

// parseMigrationFile parses a single migration file
func (scanner *MigrationScanner) parseMigrationFile(filePath, fileName string) (*migrationModel.MigrationFile, error) {
	// Parse filename using Flyway naming convention: V<version>__<description>.sql
	// Also support: R__<description>.sql for repeatable migrations
	
	var version, description, migrationType string
	
	// Versioned migration pattern: V1_0_0__create_users_table.sql
	versionedPattern := regexp.MustCompile(`^V([0-9]+(?:[_\.][0-9]+)*)__(.+)\.sql$`)
	// Repeatable migration pattern: R__create_view.sql
	repeatablePattern := regexp.MustCompile(`^R__(.+)\.sql$`)
	
	if matches := versionedPattern.FindStringSubmatch(fileName); matches != nil {
		version = strings.ReplaceAll(matches[1], "_", ".")
		description = strings.ReplaceAll(matches[2], "_", " ")
		migrationType = "SQL"
	} else if matches := repeatablePattern.FindStringSubmatch(fileName); matches != nil {
		version = "" // Repeatable migrations don't have versions
		description = strings.ReplaceAll(matches[1], "_", " ")
		migrationType = "REPEATABLE"
	} else {
		// Skip files that don't match the naming convention
		logger.ServiceLogger.Warn("[MigrationScanner] Skipping file with invalid naming convention:", fileName)
		return nil, nil
	}
	
	// Read file content
	content, err := os.ReadFile(filePath)
	if err != nil {
		return nil, fmt.Errorf("failed to read migration file %s: %w", filePath, err)
	}
	
	// Calculate checksum
	checksum := scanner.calculateChecksum(content)
	
	migration := &migrationModel.MigrationFile{
		Version:     version,
		Description: description,
		Type:        migrationType,
		Script:      fileName,
		FilePath:    filePath,
		Checksum:    checksum,
	}
	
	return migration, nil
}

// calculateChecksum calculates MD5 checksum of file content
func (scanner *MigrationScanner) calculateChecksum(content []byte) string {
	// Normalize line endings and remove trailing whitespace for consistent checksums
	normalized := strings.ReplaceAll(string(content), "\r\n", "\n")
	normalized = strings.TrimSpace(normalized)
	
	hash := md5.Sum([]byte(normalized))
	return fmt.Sprintf("%x", hash)
}

// compareVersions compares two version strings
func (scanner *MigrationScanner) compareVersions(v1, v2 string) int {
	// Handle empty versions (repeatable migrations)
	if v1 == "" && v2 == "" {
		return 0
	}
	if v1 == "" {
		return 1
	}
	if v2 == "" {
		return -1
	}
	
	parts1 := strings.Split(v1, ".")
	parts2 := strings.Split(v2, ".")
	
	maxLen := len(parts1)
	if len(parts2) > maxLen {
		maxLen = len(parts2)
	}
	
	for i := 0; i < maxLen; i++ {
		var num1, num2 int
		
		if i < len(parts1) {
			num1, _ = strconv.Atoi(parts1[i])
		}
		if i < len(parts2) {
			num2, _ = strconv.Atoi(parts2[i])
		}
		
		if num1 < num2 {
			return -1
		} else if num1 > num2 {
			return 1
		}
	}
	
	return 0
}

// GetMigrationContent reads and returns the content of a migration file
func (scanner *MigrationScanner) GetMigrationContent(migration *migrationModel.MigrationFile) (string, error) {
	content, err := os.ReadFile(migration.FilePath)
	if err != nil {
		return "", fmt.Errorf("failed to read migration file %s: %w", migration.FilePath, err)
	}
	
	return string(content), nil
}

// ValidateNamingConvention validates that all migration files follow the correct naming convention
func (scanner *MigrationScanner) ValidateNamingConvention() error {
	// Check if migrations directory exists
	if _, err := os.Stat(scanner.config.MigrationsPath); os.IsNotExist(err) {
		return nil // No migrations directory is valid
	}
	
	var invalidFiles []string
	
	err := filepath.WalkDir(scanner.config.MigrationsPath, func(path string, d fs.DirEntry, err error) error {
		if err != nil {
			return err
		}
		
		// Skip directories
		if d.IsDir() {
			return nil
		}
		
		// Only check SQL files
		if !strings.HasSuffix(strings.ToLower(d.Name()), ".sql") {
			return nil
		}
		
		// Check naming convention
		versionedPattern := regexp.MustCompile(`^V([0-9]+(?:[_\.][0-9]+)*)__(.+)\.sql$`)
		repeatablePattern := regexp.MustCompile(`^R__(.+)\.sql$`)
		
		if !versionedPattern.MatchString(d.Name()) && !repeatablePattern.MatchString(d.Name()) {
			invalidFiles = append(invalidFiles, d.Name())
		}
		
		return nil
	})
	
	if err != nil {
		return err
	}
	
	if len(invalidFiles) > 0 {
		return fmt.Errorf("invalid migration file names found: %v", invalidFiles)
	}
	
	return nil
}

// GetPendingMigrations returns migrations that haven't been applied yet
func (scanner *MigrationScanner) GetPendingMigrations(appliedMigrations []migrationModel.Migration) ([]*migrationModel.MigrationFile, error) {
	allMigrations, err := scanner.ScanMigrations()
	if err != nil {
		return nil, err
	}
	
	// Create a map of applied migration versions
	appliedVersions := make(map[string]bool)
	for _, applied := range appliedMigrations {
		appliedVersions[applied.Version] = true
	}
	
	var pendingMigrations []*migrationModel.MigrationFile
	for _, migration := range allMigrations {
		// Skip repeatable migrations for now (they need special handling)
		if migration.Type == "REPEATABLE" {
			continue
		}
		
		if !appliedVersions[migration.Version] {
			pendingMigrations = append(pendingMigrations, migration)
		}
	}
	
	return pendingMigrations, nil
}
