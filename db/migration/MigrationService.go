package migration

import (
	"fmt"
	"strings"

	"github.com/uptrace/bun"
	"patch-central-repo/logger"
	migrationModel "patch-central-repo/model/migration"
)

// MigrationService is the main service for database migrations
type MigrationService struct {
	config       *migrationModel.MigrationConfig
	repository   *MigrationRepository
	scanner      *MigrationScanner
	executor     *MigrationExecutor
	dbConnection *bun.DB
}

// NewMigrationService creates a new migration service
func NewMigrationService(config *migrationModel.MigrationConfig, dbConnection *bun.DB) *MigrationService {
	if config == nil {
		config = migrationModel.DefaultMigrationConfig()
	}

	return &MigrationService{
		config:       config,
		repository:   NewMigrationRepository(config, dbConnection),
		scanner:      NewMigrationScanner(config),
		executor:     NewMigrationExecutor(config, dbConnection),
		dbConnection: dbConnection,
	}
}

// Initialize initializes the migration system
func (service *MigrationService) Initialize() error {
	logger.ServiceLogger.Info("[MigrationService] Initializing migration system...")
	
	// Test database connection
	if err := service.executor.TestConnection(); err != nil {
		return fmt.Errorf("database connection failed: %w", err)
	}
	
	// Ensure migration table exists
	if err := service.repository.EnsureMigrationTableExists(); err != nil {
		return fmt.Errorf("failed to create migration table: %w", err)
	}
	
	// Validate naming convention
	if err := service.scanner.ValidateNamingConvention(); err != nil {
		return fmt.Errorf("migration naming convention validation failed: %w", err)
	}
	
	logger.ServiceLogger.Info("[MigrationService] Migration system initialized successfully")
	return nil
}

// Migrate executes pending migrations
func (service *MigrationService) Migrate() error {
	logger.ServiceLogger.Info("[MigrationService] Starting migration process...")
	
	// Initialize if not already done
	if err := service.Initialize(); err != nil {
		return err
	}
	
	// Validate existing migrations if configured
	if service.config.ValidateOnMigrate {
		if err := service.Validate(); err != nil {
			if service.config.CleanOnValidationError {
				logger.ServiceLogger.Warn("[MigrationService] Validation failed, cleaning and retrying...")
				if cleanErr := service.Clean(); cleanErr != nil {
					return fmt.Errorf("validation failed and clean failed: %w", cleanErr)
				}
			} else {
				return fmt.Errorf("validation failed: %w", err)
			}
		}
	}
	
	// Get applied migrations
	appliedMigrations, err := service.repository.GetAppliedMigrations()
	if err != nil {
		return fmt.Errorf("failed to get applied migrations: %w", err)
	}
	
	// Get pending migrations
	pendingMigrations, err := service.scanner.GetPendingMigrations(appliedMigrations)
	if err != nil {
		return fmt.Errorf("failed to scan pending migrations: %w", err)
	}
	
	if len(pendingMigrations) == 0 {
		logger.ServiceLogger.Info("[MigrationService] No pending migrations found. Database is up to date.")
		return nil
	}
	
	logger.ServiceLogger.Info("[MigrationService] Found", len(pendingMigrations), "pending migrations")
	
	// Execute pending migrations
	results, err := service.executor.ExecuteMigrations(pendingMigrations)
	if err != nil {
		return fmt.Errorf("migration execution failed: %w", err)
	}
	
	// Record successful migrations
	successCount := 0
	for _, result := range results {
		if result.Success {
			if recordErr := service.repository.RecordMigration(result.Migration, result.ExecutionTime, true); recordErr != nil {
				logger.ServiceLogger.Error("[MigrationService] Failed to record migration:", result.Migration.Version, recordErr.Error())
			} else {
				successCount++
			}
		} else {
			// Record failed migration
			if recordErr := service.repository.RecordMigration(result.Migration, result.ExecutionTime, false); recordErr != nil {
				logger.ServiceLogger.Error("[MigrationService] Failed to record failed migration:", result.Migration.Version, recordErr.Error())
			}
		}
	}
	
	logger.ServiceLogger.Info("[MigrationService] Migration completed successfully.", successCount, "migrations applied")
	return nil
}

// Info returns information about migration status
func (service *MigrationService) Info() ([]*migrationModel.MigrationInfo, error) {
	logger.ServiceLogger.Info("[MigrationService] Gathering migration information...")
	
	// Initialize if not already done
	if err := service.Initialize(); err != nil {
		return nil, err
	}
	
	// Get all migration files
	allMigrations, err := service.scanner.ScanMigrations()
	if err != nil {
		return nil, fmt.Errorf("failed to scan migrations: %w", err)
	}
	
	// Get applied migrations
	appliedMigrations, err := service.repository.GetAppliedMigrations()
	if err != nil {
		return nil, fmt.Errorf("failed to get applied migrations: %w", err)
	}
	
	// Create a map of applied migrations
	appliedMap := make(map[string]*migrationModel.Migration)
	for i := range appliedMigrations {
		appliedMap[appliedMigrations[i].Version] = &appliedMigrations[i]
	}
	
	var migrationInfos []*migrationModel.MigrationInfo
	
	// Process all migration files
	for _, migration := range allMigrations {
		info := &migrationModel.MigrationInfo{
			Version:     migration.Version,
			Description: migration.Description,
			Type:        migration.Type,
			Script:      migration.Script,
			Checksum:    migration.Checksum,
		}
		
		if applied, exists := appliedMap[migration.Version]; exists {
			// Migration has been applied
			if applied.Success {
				info.State = migrationModel.MigrationStatusSuccess
			} else {
				info.State = migrationModel.MigrationStatusFailed
			}
			info.InstalledOn = &applied.InstalledOn
			info.ExecutionTime = &applied.ExecutionTime
		} else {
			// Migration is pending
			info.State = migrationModel.MigrationStatusPending
		}
		
		migrationInfos = append(migrationInfos, info)
	}
	
	// Check for missing migrations (applied but file not found)
	for _, applied := range appliedMigrations {
		found := false
		for _, migration := range allMigrations {
			if migration.Version == applied.Version {
				found = true
				break
			}
		}
		
		if !found {
			info := &migrationModel.MigrationInfo{
				Version:       applied.Version,
				Description:   applied.Description,
				Type:          applied.Type,
				Script:        applied.Script,
				State:         migrationModel.MigrationStatusMissing,
				InstalledOn:   &applied.InstalledOn,
				ExecutionTime: &applied.ExecutionTime,
			}
			migrationInfos = append(migrationInfos, info)
		}
	}
	
	logger.ServiceLogger.Info("[MigrationService] Found", len(migrationInfos), "migrations")
	return migrationInfos, nil
}

// Validate validates the current migration state
func (service *MigrationService) Validate() error {
	logger.ServiceLogger.Info("[MigrationService] Validating migrations...")
	
	// Get migration info
	migrationInfos, err := service.Info()
	if err != nil {
		return fmt.Errorf("failed to get migration info: %w", err)
	}
	
	var validationErrors []string
	
	// Check for missing migrations
	for _, info := range migrationInfos {
		if info.State == migrationModel.MigrationStatusMissing {
			validationErrors = append(validationErrors, fmt.Sprintf("Missing migration file: %s", info.Version))
		}
		
		if info.State == migrationModel.MigrationStatusFailed {
			validationErrors = append(validationErrors, fmt.Sprintf("Failed migration: %s", info.Version))
		}
	}
	
	// Validate checksums if configured
	if service.config.ValidateChecksums {
		if err := service.validateChecksums(); err != nil {
			validationErrors = append(validationErrors, err.Error())
		}
	}
	
	if len(validationErrors) > 0 {
		return fmt.Errorf("validation failed: %s", strings.Join(validationErrors, "; "))
	}
	
	logger.ServiceLogger.Info("[MigrationService] Validation completed successfully")
	return nil
}

// validateChecksums validates that migration file checksums match recorded checksums
func (service *MigrationService) validateChecksums() error {
	appliedMigrations, err := service.repository.GetAppliedMigrations()
	if err != nil {
		return fmt.Errorf("failed to get applied migrations: %w", err)
	}
	
	allMigrations, err := service.scanner.ScanMigrations()
	if err != nil {
		return fmt.Errorf("failed to scan migrations: %w", err)
	}
	
	// Create a map of file migrations
	fileMap := make(map[string]*migrationModel.MigrationFile)
	for _, migration := range allMigrations {
		fileMap[migration.Version] = migration
	}
	
	var checksumErrors []string
	
	for _, applied := range appliedMigrations {
		if file, exists := fileMap[applied.Version]; exists {
			if applied.Checksum != "" && file.Checksum != applied.Checksum {
				checksumErrors = append(checksumErrors, fmt.Sprintf("Checksum mismatch for migration %s", applied.Version))
			}
		}
	}
	
	if len(checksumErrors) > 0 {
		return fmt.Errorf("checksum validation failed: %s", strings.Join(checksumErrors, "; "))
	}
	
	return nil
}

// Baseline creates a baseline migration
func (service *MigrationService) Baseline() error {
	logger.ServiceLogger.Info("[MigrationService] Creating baseline...")
	
	// Initialize if not already done
	if err := service.Initialize(); err != nil {
		return err
	}
	
	// Check if any migrations are already applied
	count, err := service.repository.GetMigrationCount()
	if err != nil {
		return fmt.Errorf("failed to get migration count: %w", err)
	}
	
	if count > 0 {
		return fmt.Errorf("cannot baseline: migrations have already been applied")
	}
	
	// Record baseline
	if err := service.repository.RecordBaseline(service.config.BaselineVersion, service.config.BaselineDescription); err != nil {
		return fmt.Errorf("failed to record baseline: %w", err)
	}
	
	logger.ServiceLogger.Info("[MigrationService] Baseline created successfully")
	return nil
}

// Clean removes all database objects (USE WITH EXTREME CAUTION)
func (service *MigrationService) Clean() error {
	logger.ServiceLogger.Warn("[MigrationService] CLEANING DATABASE - ALL DATA WILL BE LOST!")
	
	// This is a dangerous operation - in production, you might want additional safeguards
	if err := service.repository.CleanMigrationHistory(); err != nil {
		return fmt.Errorf("failed to clean migration history: %w", err)
	}
	
	logger.ServiceLogger.Info("[MigrationService] Database cleaned successfully")
	return nil
}

// Repair repairs the migration history table
func (service *MigrationService) Repair() error {
	logger.ServiceLogger.Info("[MigrationService] Repairing migration history...")
	
	// Initialize if not already done
	if err := service.Initialize(); err != nil {
		return err
	}
	
	// Get current state
	migrationInfos, err := service.Info()
	if err != nil {
		return fmt.Errorf("failed to get migration info: %w", err)
	}
	
	repairCount := 0
	
	// Fix checksum mismatches
	for _, info := range migrationInfos {
		if info.State == migrationModel.MigrationStatusSuccess {
			// Update checksum if it's different
			if err := service.repository.UpdateMigrationChecksum(info.Version, info.Checksum); err != nil {
				logger.ServiceLogger.Error("[MigrationService] Failed to update checksum for:", info.Version, err.Error())
			} else {
				repairCount++
			}
		}
	}
	
	logger.ServiceLogger.Info("[MigrationService] Repair completed.", repairCount, "migrations repaired")
	return nil
}
