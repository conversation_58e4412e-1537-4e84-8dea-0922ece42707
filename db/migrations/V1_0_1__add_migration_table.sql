-- Add migration history table for Flyway-like functionality
-- This migration creates the flyway_schema_history table to track migrations

CREATE TABLE IF NOT EXISTS flyway_schema_history (
    installed_rank SERIAL PRIMARY KEY,
    version VARCHAR(50),
    description VARCHAR(200) NOT NULL,
    type VARCHAR(20) NOT NULL,
    script VARCHAR(1000) NOT NULL,
    checksum VARCHAR(100),
    installed_by VARCHAR(100) NOT NULL,
    installed_on TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    execution_time INTEGER NOT NULL,
    success BOOLEAN NOT NULL
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_flyway_schema_history_version ON flyway_schema_history(version);
CREATE INDEX IF NOT EXISTS idx_flyway_schema_history_success ON flyway_schema_history(success);
CREATE INDEX IF NOT EXISTS idx_flyway_schema_history_installed_on ON flyway_schema_history(installed_on);

-- Add some constraints
ALTER TABLE flyway_schema_history 
ADD CONSTRAINT chk_flyway_type CHECK (type IN ('SQL', 'BASELINE', 'REPEATABLE'));

-- Add comment to the table
COMMENT ON TABLE flyway_schema_history IS 'Flyway schema history table to track database migrations';
COMMENT ON COLUMN flyway_schema_history.installed_rank IS 'Rank of the migration when it was applied';
COMMENT ON COLUMN flyway_schema_history.version IS 'Version of the migration';
COMMENT ON COLUMN flyway_schema_history.description IS 'Description of the migration';
COMMENT ON COLUMN flyway_schema_history.type IS 'Type of migration (SQL, BASELINE, REPEATABLE)';
COMMENT ON COLUMN flyway_schema_history.script IS 'Name of the migration script file';
COMMENT ON COLUMN flyway_schema_history.checksum IS 'Checksum of the migration script';
COMMENT ON COLUMN flyway_schema_history.installed_by IS 'User who applied the migration';
COMMENT ON COLUMN flyway_schema_history.installed_on IS 'Timestamp when the migration was applied';
COMMENT ON COLUMN flyway_schema_history.execution_time IS 'Time taken to execute the migration in milliseconds';
COMMENT ON COLUMN flyway_schema_history.success IS 'Whether the migration was successful';
