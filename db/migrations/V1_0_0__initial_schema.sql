-- Initial schema migration
-- This migration creates the basic database structure

-- Create users table
CREATE TABLE IF NOT EXISTS users (
    id BIGSERIAL PRIMARY KEY,
    name VA<PERSON>HAR(500) NOT NULL,
    created_time BIGINT NOT NULL,
    updated_time BIGINT NOT NULL,
    username VARCHA<PERSON>(100) UNIQUE NOT NULL,
    password VARCHAR(255) NOT NULL,
    email VARCHAR(255),
    role VARCHAR(50) DEFAULT 'USER',
    active BOOLEAN DEFAULT true,
    last_login BIGINT
);

-- <PERSON>reate supported_third_party_application_details table
CREATE TABLE IF NOT EXISTS supported_third_party_application_details (
    id BIGSERIAL PRIMARY KEY,
    name VARCHAR(500) NOT NULL,
    created_time BIGINT NOT NULL,
    updated_time BIGINT NOT NULL,
    application INTEGER NOT NULL,
    os_list TEXT[],
    arch_list TEXT[],
    language_code_list TEXT[],
    support_url VARCHAR(500)
);

-- <PERSON>reate third_party_packages table
CREATE TABLE IF NOT EXISTS third_party_packages (
    id BIGSERIAL PRIMARY KEY,
    name VA<PERSON><PERSON><PERSON>(500) NOT NULL,
    created_time BIGINT NOT NULL,
    updated_time BIGINT NOT NULL,
    description VARCHAR(500),
    version VARCHAR(100),
    os INTEGER,
    arch INTEGER,
    language_code VARCHAR(50),
    pkg_file_data JSONB,
    latest_package_url VARCHAR(1000),
    publisher VARCHAR(100),
    support_url VARCHAR(500),
    release_note VARCHAR(100),
    release_date BIGINT,
    application INTEGER,
    cve_details JSONB,
    uuid VARCHAR(500),
    os_version VARCHAR(50),
    product_code VARCHAR(255),
    install_command VARCHAR(1000),
    un_install_command VARCHAR(1000)
);

-- Create windows_patches table
CREATE TABLE IF NOT EXISTS windows_patches (
    id BIGSERIAL PRIMARY KEY,
    name VARCHAR(500) NOT NULL,
    created_time BIGINT NOT NULL,
    updated_time BIGINT NOT NULL,
    temp_data BOOLEAN DEFAULT false,
    replace_by_id BIGINT,
    replace_by_uuid VARCHAR(100),
    revision_id BIGINT,
    uuid VARCHAR(100) UNIQUE NOT NULL,
    title VARCHAR(1000),
    kb_id VARCHAR(50),
    description TEXT,
    release_date BIGINT,
    last_modified_date BIGINT,
    creation_date BIGINT,
    is_latest_revision BOOLEAN DEFAULT true,
    patch_category_id BIGINT,
    patch_product_id BIGINT,
    language_id BIGINT,
    file_data JSONB,
    cve_details JSONB,
    superseded_by_uuid VARCHAR(100),
    supersedes_uuid VARCHAR(100),
    is_declined BOOLEAN DEFAULT false,
    is_approved BOOLEAN DEFAULT false,
    approval_date BIGINT,
    decline_date BIGINT
);

-- Create patch_categories table
CREATE TABLE IF NOT EXISTS patch_categories (
    id BIGSERIAL PRIMARY KEY,
    name VARCHAR(500) NOT NULL,
    created_time BIGINT NOT NULL,
    updated_time BIGINT NOT NULL,
    category_id VARCHAR(100) UNIQUE NOT NULL,
    parent_category_id VARCHAR(100),
    description TEXT
);

-- Create patch_products table
CREATE TABLE IF NOT EXISTS patch_products (
    id BIGSERIAL PRIMARY KEY,
    name VARCHAR(500) NOT NULL,
    created_time BIGINT NOT NULL,
    updated_time BIGINT NOT NULL,
    product_id VARCHAR(100) UNIQUE NOT NULL,
    family_name VARCHAR(200),
    description TEXT
);

-- Create languages table
CREATE TABLE IF NOT EXISTS languages (
    id BIGSERIAL PRIMARY KEY,
    name VARCHAR(500) NOT NULL,
    created_time BIGINT NOT NULL,
    updated_time BIGINT NOT NULL,
    language_code VARCHAR(10) UNIQUE NOT NULL,
    display_name VARCHAR(100)
);

-- Create wsus_sync_histories table
CREATE TABLE IF NOT EXISTS wsus_sync_histories (
    id BIGSERIAL PRIMARY KEY,
    processed_on BIGINT NOT NULL,
    wsus_offset BIGINT NOT NULL,
    wsus_size BIGINT NOT NULL,
    total_size BIGINT NOT NULL,
    update_time_of_last_record BIGINT NOT NULL
);

-- Create cab_sync_histories table
CREATE TABLE IF NOT EXISTS cab_sync_histories (
    id BIGSERIAL PRIMARY KEY,
    last_sync_time BIGINT NOT NULL,
    release_date VARCHAR(100),
    compress BOOLEAN DEFAULT false
);

-- Create wsus_uuids table
CREATE TABLE IF NOT EXISTS wsus_uuids (
    id BIGSERIAL PRIMARY KEY,
    uuid VARCHAR(100) UNIQUE NOT NULL,
    processed BOOLEAN DEFAULT false,
    created_time BIGINT NOT NULL
);

-- Create ubuntu_patches table
CREATE TABLE IF NOT EXISTS ubuntu_patches (
    id BIGSERIAL PRIMARY KEY,
    name VARCHAR(500) NOT NULL,
    created_time BIGINT NOT NULL,
    updated_time BIGINT NOT NULL,
    uuid VARCHAR(250),
    os_version VARCHAR(50),
    channel VARCHAR(50),
    repo VARCHAR(1000),
    package_name VARCHAR(500),
    arch VARCHAR(50),
    version VARCHAR(250),
    priority VARCHAR(50),
    section VARCHAR(50),
    origin VARCHAR(50),
    depends TEXT,
    pre_depends TEXT,
    recommends TEXT,
    suggests TEXT,
    conflicts TEXT,
    breaks TEXT,
    replaces TEXT,
    provides TEXT,
    installed_size BIGINT,
    maintainer VARCHAR(500),
    description TEXT,
    homepage VARCHAR(500),
    filename VARCHAR(1000),
    size BIGINT,
    md5sum VARCHAR(100),
    sha1 VARCHAR(100),
    sha256 VARCHAR(100),
    sha512 VARCHAR(100)
);

-- Create ubuntu_notice_data table
CREATE TABLE IF NOT EXISTS ubuntu_notice_data (
    id BIGSERIAL PRIMARY KEY,
    name VARCHAR(500) NOT NULL,
    created_time BIGINT NOT NULL,
    updated_time BIGINT NOT NULL,
    notice_id VARCHAR(100) UNIQUE NOT NULL,
    title VARCHAR(1000),
    description TEXT,
    summary TEXT,
    published_date BIGINT,
    updated_date BIGINT,
    cves JSONB,
    notice_references JSONB,
    packages JSONB
);

-- Create ubuntu_notice_histories table
CREATE TABLE IF NOT EXISTS ubuntu_notice_histories (
    id BIGSERIAL PRIMARY KEY,
    name VARCHAR(500) NOT NULL,
    created_time BIGINT NOT NULL,
    updated_time BIGINT NOT NULL,
    notice_offset INTEGER NOT NULL,
    notice_size INTEGER NOT NULL,
    total_size INTEGER NOT NULL
);

-- Create ubuntu_release_packages table
CREATE TABLE IF NOT EXISTS ubuntu_release_packages (
    id BIGSERIAL PRIMARY KEY,
    name VARCHAR(500) NOT NULL,
    created_time BIGINT NOT NULL,
    updated_time BIGINT NOT NULL,
    package_name VARCHAR(500),
    version VARCHAR(250),
    arch VARCHAR(50),
    release_name VARCHAR(100),
    component VARCHAR(100),
    priority VARCHAR(50),
    section VARCHAR(50),
    size BIGINT,
    installed_size BIGINT,
    maintainer VARCHAR(500),
    description TEXT,
    depends TEXT,
    pre_depends TEXT,
    recommends TEXT,
    suggests TEXT,
    conflicts TEXT,
    breaks TEXT,
    replaces TEXT,
    provides TEXT,
    filename VARCHAR(1000),
    md5sum VARCHAR(100),
    sha1 VARCHAR(100),
    sha256 VARCHAR(100)
);

-- Create linux_packages table
CREATE TABLE IF NOT EXISTS linux_packages (
    id BIGSERIAL PRIMARY KEY,
    name VARCHAR(500) NOT NULL,
    created_time BIGINT NOT NULL,
    updated_time BIGINT NOT NULL,
    description VARCHAR(1000),
    section VARCHAR(100),
    distribution VARCHAR(100),
    old_version VARCHAR(100),
    arch VARCHAR(50),
    release_date BIGINT,
    version VARCHAR(250)
);

-- Create mac_os_patches table
CREATE TABLE IF NOT EXISTS mac_os_patches (
    id BIGSERIAL PRIMARY KEY,
    name VARCHAR(500) NOT NULL,
    created_time BIGINT NOT NULL,
    updated_time BIGINT NOT NULL,
    os_version VARCHAR(50),
    product_key VARCHAR(100),
    release_date BIGINT,
    description VARCHAR(1000),
    version VARCHAR(50),
    distribution_file_name VARCHAR(500),
    product_type VARCHAR(50),
    packages JSONB
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_users_username ON users(username);
CREATE INDEX IF NOT EXISTS idx_users_email ON users(email);
CREATE INDEX IF NOT EXISTS idx_third_party_packages_application ON third_party_packages(application);
CREATE INDEX IF NOT EXISTS idx_third_party_packages_uuid ON third_party_packages(uuid);
CREATE INDEX IF NOT EXISTS idx_windows_patches_uuid ON windows_patches(uuid);
CREATE INDEX IF NOT EXISTS idx_windows_patches_kb_id ON windows_patches(kb_id);
CREATE INDEX IF NOT EXISTS idx_windows_patches_category ON windows_patches(patch_category_id);
CREATE INDEX IF NOT EXISTS idx_windows_patches_product ON windows_patches(patch_product_id);
CREATE INDEX IF NOT EXISTS idx_ubuntu_patches_package_name ON ubuntu_patches(package_name);
CREATE INDEX IF NOT EXISTS idx_ubuntu_patches_os_version ON ubuntu_patches(os_version);
CREATE INDEX IF NOT EXISTS idx_ubuntu_notice_data_notice_id ON ubuntu_notice_data(notice_id);
CREATE INDEX IF NOT EXISTS idx_mac_os_patches_product_key ON mac_os_patches(product_key);
CREATE INDEX IF NOT EXISTS idx_mac_os_patches_os_version ON mac_os_patches(os_version);
