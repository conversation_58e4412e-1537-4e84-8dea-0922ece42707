-- Add audit columns to existing tables
-- This migration adds created_by and updated_by columns for better auditing

-- Add audit columns to users table
ALTER TABLE users 
ADD COLUMN IF NOT EXISTS created_by VA<PERSON><PERSON><PERSON>(100),
ADD COLUMN IF NOT EXISTS updated_by VARCHAR(100);

-- Add audit columns to third_party_packages table
ALTER TABLE third_party_packages 
ADD COLUMN IF NOT EXISTS created_by <PERSON><PERSON><PERSON><PERSON>(100),
ADD COLUMN IF NOT EXISTS updated_by VA<PERSON>HA<PERSON>(100);

-- Add audit columns to windows_patches table
ALTER TABLE windows_patches 
ADD COLUMN IF NOT EXISTS created_by VA<PERSON><PERSON><PERSON>(100),
ADD COLUMN IF NOT EXISTS updated_by VARCHAR(100);

-- Add audit columns to patch_categories table
ALTER TABLE patch_categories 
ADD COLUMN IF NOT EXISTS created_by <PERSON><PERSON><PERSON><PERSON>(100),
ADD COLUMN IF NOT EXISTS updated_by VARCHAR(100);

-- Add audit columns to patch_products table
ALTER TABLE patch_products 
ADD COLUMN IF NOT EXISTS created_by <PERSON>RC<PERSON><PERSON>(100),
ADD COLUMN IF NOT EXISTS updated_by VARC<PERSON>R(100);

-- Add audit columns to languages table
ALTER TABLE languages 
ADD COLUMN IF NOT EXISTS created_by VARCHAR(100),
ADD COLUMN IF NOT EXISTS updated_by VARCHAR(100);

-- Add audit columns to ubuntu_patches table
ALTER TABLE ubuntu_patches 
ADD COLUMN IF NOT EXISTS created_by VARCHAR(100),
ADD COLUMN IF NOT EXISTS updated_by VARCHAR(100);

-- Add audit columns to ubuntu_notice_data table
ALTER TABLE ubuntu_notice_data 
ADD COLUMN IF NOT EXISTS created_by VARCHAR(100),
ADD COLUMN IF NOT EXISTS updated_by VARCHAR(100);

-- Add audit columns to ubuntu_notice_histories table
ALTER TABLE ubuntu_notice_histories 
ADD COLUMN IF NOT EXISTS created_by VARCHAR(100),
ADD COLUMN IF NOT EXISTS updated_by VARCHAR(100);

-- Add audit columns to ubuntu_release_packages table
ALTER TABLE ubuntu_release_packages 
ADD COLUMN IF NOT EXISTS created_by VARCHAR(100),
ADD COLUMN IF NOT EXISTS updated_by VARCHAR(100);

-- Add audit columns to linux_packages table
ALTER TABLE linux_packages 
ADD COLUMN IF NOT EXISTS created_by VARCHAR(100),
ADD COLUMN IF NOT EXISTS updated_by VARCHAR(100);

-- Add audit columns to mac_os_patches table
ALTER TABLE mac_os_patches 
ADD COLUMN IF NOT EXISTS created_by VARCHAR(100),
ADD COLUMN IF NOT EXISTS updated_by VARCHAR(100);

-- Add audit columns to supported_third_party_application_details table
ALTER TABLE supported_third_party_application_details 
ADD COLUMN IF NOT EXISTS created_by VARCHAR(100),
ADD COLUMN IF NOT EXISTS updated_by VARCHAR(100);

-- Create indexes on audit columns for better performance
CREATE INDEX IF NOT EXISTS idx_users_created_by ON users(created_by);
CREATE INDEX IF NOT EXISTS idx_users_updated_by ON users(updated_by);
CREATE INDEX IF NOT EXISTS idx_third_party_packages_created_by ON third_party_packages(created_by);
CREATE INDEX IF NOT EXISTS idx_third_party_packages_updated_by ON third_party_packages(updated_by);
CREATE INDEX IF NOT EXISTS idx_windows_patches_created_by ON windows_patches(created_by);
CREATE INDEX IF NOT EXISTS idx_windows_patches_updated_by ON windows_patches(updated_by);

-- Add comments to the new columns
COMMENT ON COLUMN users.created_by IS 'User who created this record';
COMMENT ON COLUMN users.updated_by IS 'User who last updated this record';
COMMENT ON COLUMN third_party_packages.created_by IS 'User who created this record';
COMMENT ON COLUMN third_party_packages.updated_by IS 'User who last updated this record';
COMMENT ON COLUMN windows_patches.created_by IS 'User who created this record';
COMMENT ON COLUMN windows_patches.updated_by IS 'User who last updated this record';
