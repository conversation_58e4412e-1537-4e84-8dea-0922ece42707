# Database Migrations

This directory contains database migration files for the patch-central-repo application. The migration system is based on Flyway patterns and provides version control for your database schema.

## Overview

The migration system provides:
- **Versioned migrations**: Apply changes in a specific order
- **Migration history tracking**: Keep track of what has been applied
- **Validation**: Ensure migration integrity with checksums
- **Rollback support**: Repair and baseline capabilities
- **CLI and API interfaces**: Multiple ways to manage migrations

## Migration File Naming Convention

Migration files must follow the Flyway naming convention:

### Versioned Migrations
```
V<version>__<description>.sql
```

Examples:
- `V1_0_0__initial_schema.sql`
- `V1_0_1__add_migration_table.sql`
- `V1_1_0__add_user_roles.sql`
- `V2_0_0__major_schema_update.sql`

### Repeatable Migrations
```
R__<description>.sql
```

Examples:
- `R__create_views.sql`
- `R__update_functions.sql`

## Version Numbering

- Use semantic versioning: `MAJOR_MINOR_PATCH`
- Separate version parts with underscores (`_`)
- Always start with version 1.0.0
- Increment versions sequentially

## Migration File Structure

Each migration file should:
1. Start with a comment describing the migration
2. Use transaction-safe SQL statements
3. Include proper error handling
4. Be idempotent when possible

Example:
```sql
-- Add user audit columns
-- This migration adds created_by and updated_by columns for better auditing

-- Add audit columns to users table
ALTER TABLE users 
ADD COLUMN IF NOT EXISTS created_by VARCHAR(100),
ADD COLUMN IF NOT EXISTS updated_by VARCHAR(100);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_users_created_by ON users(created_by);
CREATE INDEX IF NOT EXISTS idx_users_updated_by ON users(updated_by);
```

## Using the Migration System

### Command Line Interface

The migration system provides a CLI tool for managing migrations:

```bash
# Execute pending migrations
go run cmd/migrate.go migrate

# Show migration status
go run cmd/migrate.go info

# Validate current state
go run cmd/migrate.go validate

# Create baseline
go run cmd/migrate.go baseline

# Repair migration history
go run cmd/migrate.go repair

# Show help
go run cmd/migrate.go help
```

### REST API

The migration system also provides REST endpoints:

```bash
# Get migration information
GET /api/migrations/info

# Execute pending migrations
POST /api/migrations/migrate

# Validate migrations
POST /api/migrations/validate

# Create baseline
POST /api/migrations/baseline

# Repair migration history
POST /api/migrations/repair

# Check migration health
GET /api/migrations/health
```

### Programmatic Usage

You can also use the migration system programmatically:

```go
import (
    "patch-central-repo/db"
    "patch-central-repo/db/migration"
    migrationModel "patch-central-repo/model/migration"
)

// Get migration information
migrationInfos, err := db.GetMigrationInfo()

// Validate migrations
err := db.ValidateMigrations()

// Create baseline
err := db.BaselineMigrations()

// Repair migrations
err := db.RepairMigrations()
```

## Migration Commands

### migrate
Executes all pending migrations in order. This is the most commonly used command.

### info
Shows the status of all migrations:
- **SUCCESS**: Migration has been applied successfully
- **PENDING**: Migration is waiting to be applied
- **FAILED**: Migration failed during execution
- **MISSING**: Migration file is missing but was previously applied
- **BASELINE**: Baseline migration marker

### validate
Validates the current migration state by:
- Checking for missing migration files
- Validating checksums
- Identifying failed migrations

### baseline
Creates a baseline migration at a specific version. Useful when starting with an existing database.

### repair
Repairs the migration history by:
- Updating checksums for existing migrations
- Removing failed migration entries
- Fixing inconsistencies

### clean
**DANGEROUS**: Removes all migration history. Use with extreme caution.

## Best Practices

### Writing Migrations

1. **Make migrations idempotent**: Use `IF NOT EXISTS`, `IF EXISTS` clauses
2. **Use transactions**: Wrap related changes in transactions
3. **Test migrations**: Test on a copy of production data
4. **Keep migrations small**: One logical change per migration
5. **Don't modify applied migrations**: Create new migrations for changes

### Version Management

1. **Sequential versioning**: Don't skip version numbers
2. **Team coordination**: Coordinate version numbers in teams
3. **Branch management**: Merge migration conflicts carefully
4. **Documentation**: Document complex migrations

### Production Deployment

1. **Backup first**: Always backup before running migrations
2. **Test in staging**: Run migrations in staging environment first
3. **Monitor execution**: Watch for long-running migrations
4. **Have rollback plan**: Prepare rollback procedures

## Configuration

The migration system can be configured through environment variables or code:

```go
config := &migrationModel.MigrationConfig{
    MigrationsPath:          "db/migrations",
    TableName:               "flyway_schema_history",
    BaselineVersion:         "1",
    BaselineDescription:     "<< Flyway Baseline >>",
    ValidateOnMigrate:       true,
    CleanOnValidationError:  false,
    OutOfOrder:              false,
    IgnoreMissingMigrations: false,
    IgnoreIgnoredMigrations: false,
    IgnorePendingMigrations: false,
    IgnoreFutureMigrations:  false,
    ValidateChecksums:       true,
    InstalledBy:             "flyway-go",
}
```

## Troubleshooting

### Common Issues

1. **Checksum mismatch**: Migration file was modified after being applied
   - Solution: Use `repair` command to update checksums

2. **Missing migration file**: File was deleted but migration was applied
   - Solution: Restore the file or use `repair` command

3. **Failed migration**: Migration failed during execution
   - Solution: Fix the issue and use `repair` to remove failed entry

4. **Out of order migrations**: Migrations applied in wrong order
   - Solution: Use `repair` command or set `OutOfOrder: true`

### Getting Help

- Check the logs for detailed error messages
- Use the `info` command to see current state
- Use the `validate` command to identify issues
- Consult the application logs for migration execution details

## Migration History Table

The system tracks migrations in the `flyway_schema_history` table:

| Column | Description |
|--------|-------------|
| installed_rank | Order in which migration was applied |
| version | Migration version |
| description | Migration description |
| type | Migration type (SQL, BASELINE, REPEATABLE) |
| script | Migration script filename |
| checksum | Migration file checksum |
| installed_by | User who applied the migration |
| installed_on | Timestamp when migration was applied |
| execution_time | Time taken to execute (milliseconds) |
| success | Whether migration was successful |
