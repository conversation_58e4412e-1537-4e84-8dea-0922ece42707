package rest

import (
	"encoding/json"
	"fmt"
	"net/http"
	"time"

	"github.com/gorilla/mux"
	"github.com/uptrace/bun"
	"patch-central-repo/db/migration"
	"patch-central-repo/logger"
	migrationModel "patch-central-repo/model/migration"
)

// MigrationController handles migration-related HTTP requests
type MigrationController struct {
	migrationService *migration.MigrationService
}

// NewMigrationController creates a new migration controller
func NewMigrationController(dbConnection *bun.DB) *MigrationController {
	config := migrationModel.DefaultMigrationConfig()
	return &MigrationController{
		migrationService: migration.NewMigrationService(config, dbConnection),
	}
}

// MigrationInfoResponse represents the response for migration info
type MigrationInfoResponse struct {
	Migrations   []*migrationModel.MigrationInfo `json:"migrations"`
	TotalCount   int                             `json:"totalCount"`
	Summary      map[string]int                  `json:"summary"`
	LastUpdated  time.Time                       `json:"lastUpdated"`
}

// MigrationOperationResponse represents the response for migration operations
type MigrationOperationResponse struct {
	Success     bool      `json:"success"`
	Message     string    `json:"message"`
	Duration    string    `json:"duration,omitempty"`
	Timestamp   time.Time `json:"timestamp"`
	Error       string    `json:"error,omitempty"`
}

// RegisterMigrationRoutes registers migration-related routes
func RegisterMigrationRoutes(router *mux.Router, dbConnection *bun.DB) {
	controller := NewMigrationController(dbConnection)
	
	// Migration routes
	migrationRouter := router.PathPrefix("/api/migrations").Subrouter()
	
	// GET /api/migrations/info - Get migration status information
	migrationRouter.HandleFunc("/info", controller.GetMigrationInfo).Methods("GET")
	
	// POST /api/migrations/migrate - Execute pending migrations
	migrationRouter.HandleFunc("/migrate", controller.ExecuteMigrations).Methods("POST")
	
	// POST /api/migrations/validate - Validate current migration state
	migrationRouter.HandleFunc("/validate", controller.ValidateMigrations).Methods("POST")
	
	// POST /api/migrations/baseline - Create baseline migration
	migrationRouter.HandleFunc("/baseline", controller.CreateBaseline).Methods("POST")
	
	// POST /api/migrations/repair - Repair migration history
	migrationRouter.HandleFunc("/repair", controller.RepairMigrations).Methods("POST")
	
	// GET /api/migrations/health - Check migration system health
	migrationRouter.HandleFunc("/health", controller.GetMigrationHealth).Methods("GET")
}

// GetMigrationInfo returns migration status information
func (controller *MigrationController) GetMigrationInfo(w http.ResponseWriter, r *http.Request) {
	logger.ServiceLogger.Info("[MigrationController] Getting migration info")
	
	start := time.Now()
	
	// Get migration information
	migrationInfos, err := controller.migrationService.Info()
	if err != nil {
		logger.ServiceLogger.Error("[MigrationController] Error getting migration info:", err.Error())
		http.Error(w, fmt.Sprintf("Failed to get migration info: %s", err.Error()), http.StatusInternalServerError)
		return
	}
	
	// Calculate summary
	summary := make(map[string]int)
	for _, info := range migrationInfos {
		summary[string(info.State)]++
	}
	
	response := MigrationInfoResponse{
		Migrations:  migrationInfos,
		TotalCount:  len(migrationInfos),
		Summary:     summary,
		LastUpdated: time.Now(),
	}
	
	w.Header().Set("Content-Type", "application/json")
	if err := json.NewEncoder(w).Encode(response); err != nil {
		logger.ServiceLogger.Error("[MigrationController] Error encoding response:", err.Error())
		http.Error(w, "Failed to encode response", http.StatusInternalServerError)
		return
	}
	
	logger.ServiceLogger.Info("[MigrationController] Migration info retrieved successfully in", time.Since(start))
}

// ExecuteMigrations executes pending migrations
func (controller *MigrationController) ExecuteMigrations(w http.ResponseWriter, r *http.Request) {
	logger.ServiceLogger.Info("[MigrationController] Executing migrations")
	
	start := time.Now()
	
	// Execute migrations
	err := controller.migrationService.Migrate()
	duration := time.Since(start)
	
	response := MigrationOperationResponse{
		Success:   err == nil,
		Duration:  duration.String(),
		Timestamp: time.Now(),
	}
	
	if err != nil {
		logger.ServiceLogger.Error("[MigrationController] Migration execution failed:", err.Error())
		response.Message = "Migration execution failed"
		response.Error = err.Error()
		w.WriteHeader(http.StatusInternalServerError)
	} else {
		logger.ServiceLogger.Info("[MigrationController] Migrations executed successfully in", duration)
		response.Message = "Migrations executed successfully"
		w.WriteHeader(http.StatusOK)
	}
	
	w.Header().Set("Content-Type", "application/json")
	if encodeErr := json.NewEncoder(w).Encode(response); encodeErr != nil {
		logger.ServiceLogger.Error("[MigrationController] Error encoding response:", encodeErr.Error())
	}
}

// ValidateMigrations validates the current migration state
func (controller *MigrationController) ValidateMigrations(w http.ResponseWriter, r *http.Request) {
	logger.ServiceLogger.Info("[MigrationController] Validating migrations")
	
	start := time.Now()
	
	// Validate migrations
	err := controller.migrationService.Validate()
	duration := time.Since(start)
	
	response := MigrationOperationResponse{
		Success:   err == nil,
		Duration:  duration.String(),
		Timestamp: time.Now(),
	}
	
	if err != nil {
		logger.ServiceLogger.Error("[MigrationController] Migration validation failed:", err.Error())
		response.Message = "Migration validation failed"
		response.Error = err.Error()
		w.WriteHeader(http.StatusBadRequest)
	} else {
		logger.ServiceLogger.Info("[MigrationController] Migrations validated successfully in", duration)
		response.Message = "Migrations validated successfully"
		w.WriteHeader(http.StatusOK)
	}
	
	w.Header().Set("Content-Type", "application/json")
	if encodeErr := json.NewEncoder(w).Encode(response); encodeErr != nil {
		logger.ServiceLogger.Error("[MigrationController] Error encoding response:", encodeErr.Error())
	}
}

// CreateBaseline creates a baseline migration
func (controller *MigrationController) CreateBaseline(w http.ResponseWriter, r *http.Request) {
	logger.ServiceLogger.Info("[MigrationController] Creating baseline")
	
	start := time.Now()
	
	// Create baseline
	err := controller.migrationService.Baseline()
	duration := time.Since(start)
	
	response := MigrationOperationResponse{
		Success:   err == nil,
		Duration:  duration.String(),
		Timestamp: time.Now(),
	}
	
	if err != nil {
		logger.ServiceLogger.Error("[MigrationController] Baseline creation failed:", err.Error())
		response.Message = "Baseline creation failed"
		response.Error = err.Error()
		w.WriteHeader(http.StatusBadRequest)
	} else {
		logger.ServiceLogger.Info("[MigrationController] Baseline created successfully in", duration)
		response.Message = "Baseline created successfully"
		w.WriteHeader(http.StatusCreated)
	}
	
	w.Header().Set("Content-Type", "application/json")
	if encodeErr := json.NewEncoder(w).Encode(response); encodeErr != nil {
		logger.ServiceLogger.Error("[MigrationController] Error encoding response:", encodeErr.Error())
	}
}

// RepairMigrations repairs the migration history
func (controller *MigrationController) RepairMigrations(w http.ResponseWriter, r *http.Request) {
	logger.ServiceLogger.Info("[MigrationController] Repairing migrations")
	
	start := time.Now()
	
	// Repair migrations
	err := controller.migrationService.Repair()
	duration := time.Since(start)
	
	response := MigrationOperationResponse{
		Success:   err == nil,
		Duration:  duration.String(),
		Timestamp: time.Now(),
	}
	
	if err != nil {
		logger.ServiceLogger.Error("[MigrationController] Migration repair failed:", err.Error())
		response.Message = "Migration repair failed"
		response.Error = err.Error()
		w.WriteHeader(http.StatusInternalServerError)
	} else {
		logger.ServiceLogger.Info("[MigrationController] Migrations repaired successfully in", duration)
		response.Message = "Migrations repaired successfully"
		w.WriteHeader(http.StatusOK)
	}
	
	w.Header().Set("Content-Type", "application/json")
	if encodeErr := json.NewEncoder(w).Encode(response); encodeErr != nil {
		logger.ServiceLogger.Error("[MigrationController] Error encoding response:", encodeErr.Error())
	}
}

// GetMigrationHealth checks the health of the migration system
func (controller *MigrationController) GetMigrationHealth(w http.ResponseWriter, r *http.Request) {
	logger.ServiceLogger.Info("[MigrationController] Checking migration health")

	start := time.Now()

	// Check if migration table exists and is accessible
	migrationInfos, err := controller.migrationService.Info()
	duration := time.Since(start)
	
	health := map[string]interface{}{
		"healthy":     err == nil,
		"timestamp":   time.Now(),
		"duration":    duration.String(),
		"migrations":  len(migrationInfos),
	}
	
	if err != nil {
		logger.ServiceLogger.Error("[MigrationController] Migration health check failed:", err.Error())
		health["error"] = err.Error()
		w.WriteHeader(http.StatusServiceUnavailable)
	} else {
		logger.ServiceLogger.Info("[MigrationController] Migration health check passed in", duration)
		w.WriteHeader(http.StatusOK)
	}
	
	w.Header().Set("Content-Type", "application/json")
	if encodeErr := json.NewEncoder(w).Encode(health); encodeErr != nil {
		logger.ServiceLogger.Error("[MigrationController] Error encoding health response:", encodeErr.Error())
	}
}
