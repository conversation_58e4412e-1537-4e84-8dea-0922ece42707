package main

import (
	"fmt"
	"os"
	"path/filepath"
	"strings"
	"time"
	
	"github.com/joho/godotenv"
	"patch-central-repo/common"
	"patch-central-repo/db"
	"patch-central-repo/db/migration"
	"patch-central-repo/logger"
	migrationModel "patch-central-repo/model/migration"
)

func main() {
	if len(os.Args) < 2 {
		printUsage()
		os.Exit(1)
	}
	
	command := os.Args[1]
	
	// Initialize environment
	if err := initializeEnvironment(); err != nil {
		fmt.Printf("Error initializing environment: %s\n", err.Error())
		os.Exit(1)
	}
	
	// Initialize database connection
	dbConnection, err := db.Connect()
	if err != nil {
		fmt.Printf("Error connecting to database: %s\n", err.Error())
		os.Exit(1)
	}

	// Create migration service
	config := migrationModel.DefaultMigrationConfig()
	migrationService := migration.NewMigrationService(config, dbConnection)
	
	// Execute command
	switch strings.ToLower(command) {
	case "migrate":
		handleMigrate(migrationService)
	case "info":
		handleInfo(migrationService)
	case "validate":
		handleValidate(migrationService)
	case "baseline":
		handleBaseline(migrationService)
	case "repair":
		handleRepair(migrationService)
	case "clean":
		handleClean(migrationService)
	case "help", "-h", "--help":
		printUsage()
	default:
		fmt.Printf("Unknown command: %s\n", command)
		printUsage()
		os.Exit(1)
	}
}

func initializeEnvironment() error {
	workingDir := common.CurrentWorkingDir()
	
	// Load app.config file
	err := godotenv.Load(filepath.Join(workingDir, "app.config"))
	if err != nil {
		return fmt.Errorf("error loading app.config file: %w", err)
	}
	
	// Initialize logger
	logDir := filepath.Join(workingDir, "logs")
	if _, err := os.Stat(logDir); os.IsNotExist(err) {
		err := os.Mkdir(logDir, os.ModePerm)
		if err != nil {
			return fmt.Errorf("error creating log directory: %w", err)
		}
	}
	
	logLevel := common.GetEnv("LOG_LEVEL", "info")
	logMaxSize := common.GetEnvNumeric("LOG_MAX_SIZE", 10)
	logMaxAge := common.GetEnvNumeric("LOG_MAX_AGE", 30)
	logger.ConfigLogger(logDir, logLevel, logMaxSize, logMaxAge)
	
	return nil
}

func handleMigrate(service *migration.MigrationService) {
	fmt.Println("Starting database migration...")
	
	start := time.Now()
	err := service.Migrate()
	duration := time.Since(start)
	
	if err != nil {
		fmt.Printf("Migration failed: %s\n", err.Error())
		os.Exit(1)
	}
	
	fmt.Printf("Migration completed successfully in %v\n", duration)
}

func handleInfo(service *migration.MigrationService) {
	fmt.Println("Migration Status Information:")
	fmt.Println("=============================")
	
	migrationInfos, err := service.Info()
	if err != nil {
		fmt.Printf("Error getting migration info: %s\n", err.Error())
		os.Exit(1)
	}
	
	if len(migrationInfos) == 0 {
		fmt.Println("No migrations found.")
		return
	}
	
	// Print header
	fmt.Printf("%-15s %-10s %-30s %-20s %-20s\n", "Version", "State", "Description", "Installed On", "Execution Time")
	fmt.Println(strings.Repeat("-", 95))
	
	// Print migration info
	for _, info := range migrationInfos {
		installedOn := "N/A"
		executionTime := "N/A"
		
		if info.InstalledOn != nil {
			installedOn = info.InstalledOn.Format("2006-01-02 15:04:05")
		}
		
		if info.ExecutionTime != nil {
			executionTime = fmt.Sprintf("%d ms", *info.ExecutionTime)
		}
		
		fmt.Printf("%-15s %-10s %-30s %-20s %-20s\n", 
			info.Version, 
			string(info.State), 
			truncateString(info.Description, 30),
			installedOn,
			executionTime)
	}
	
	// Print summary
	fmt.Println()
	fmt.Printf("Total migrations: %d\n", len(migrationInfos))
	
	// Count by status
	statusCounts := make(map[migrationModel.MigrationStatus]int)
	for _, info := range migrationInfos {
		statusCounts[info.State]++
	}
	
	for status, count := range statusCounts {
		fmt.Printf("%s: %d\n", string(status), count)
	}
}

func handleValidate(service *migration.MigrationService) {
	fmt.Println("Validating migrations...")
	
	err := service.Validate()
	if err != nil {
		fmt.Printf("Validation failed: %s\n", err.Error())
		os.Exit(1)
	}
	
	fmt.Println("Validation completed successfully. All migrations are valid.")
}

func handleBaseline(service *migration.MigrationService) {
	fmt.Println("Creating baseline...")
	
	err := service.Baseline()
	if err != nil {
		fmt.Printf("Baseline creation failed: %s\n", err.Error())
		os.Exit(1)
	}
	
	fmt.Println("Baseline created successfully.")
}

func handleRepair(service *migration.MigrationService) {
	fmt.Println("Repairing migration history...")
	
	err := service.Repair()
	if err != nil {
		fmt.Printf("Repair failed: %s\n", err.Error())
		os.Exit(1)
	}
	
	fmt.Println("Migration history repaired successfully.")
}

func handleClean(service *migration.MigrationService) {
	fmt.Println("WARNING: This will remove all migration history!")
	fmt.Print("Are you sure you want to continue? (yes/no): ")
	
	var response string
	fmt.Scanln(&response)
	
	if strings.ToLower(response) != "yes" {
		fmt.Println("Clean operation cancelled.")
		return
	}
	
	fmt.Println("Cleaning migration history...")
	
	err := service.Clean()
	if err != nil {
		fmt.Printf("Clean failed: %s\n", err.Error())
		os.Exit(1)
	}
	
	fmt.Println("Migration history cleaned successfully.")
}

func truncateString(s string, maxLen int) string {
	if len(s) <= maxLen {
		return s
	}
	return s[:maxLen-3] + "..."
}

func printUsage() {
	fmt.Println("Database Migration Tool")
	fmt.Println("=======================")
	fmt.Println()
	fmt.Println("Usage: go run cmd/migrate.go <command>")
	fmt.Println()
	fmt.Println("Commands:")
	fmt.Println("  migrate   - Execute pending migrations")
	fmt.Println("  info      - Show migration status information")
	fmt.Println("  validate  - Validate current migration state")
	fmt.Println("  baseline  - Create a baseline migration")
	fmt.Println("  repair    - Repair migration history")
	fmt.Println("  clean     - Clean migration history (DANGEROUS)")
	fmt.Println("  help      - Show this help message")
	fmt.Println()
	fmt.Println("Examples:")
	fmt.Println("  go run cmd/migrate.go migrate")
	fmt.Println("  go run cmd/migrate.go info")
	fmt.Println("  go run cmd/migrate.go validate")
	fmt.Println()
	fmt.Println("Migration files should be placed in db/migrations/ directory")
	fmt.Println("and follow the naming convention: V<version>__<description>.sql")
	fmt.Println("Example: V1_0_0__initial_schema.sql")
}
